<div class="flex flex-col items-center pb-32">
  @if (isLoading()) {
    <app-booking-confirmation-skeleton></app-booking-confirmation-skeleton>
  } @else {
    <img src="/Success.svg" alt="Calendar icon" class="w-12 h-12 mt-12" />

    <h2 class="mt-3 text-2xl leading-10 text-[#0BAB68]">Congratulations!</h2>
    <h2 class="text-lg font-semibold leading-6 text-[#171717]">Your booking is confirmed</h2>
    <h2 class="mt-3 text-center text-sm leading-5 text-[#747474]">
      You will receive a confirmation mail with your booking ID.
    </h2>

    <div class="mt-6 w-full max-w-[21.5625rem] bg-white p-2 squircle-lg">
      <img src="/card.jpg" alt="Reservation" class="h-[10.75rem] w-full object-cover squircle-lg" />

      <div class="ml-4">
        <h2 class="mt-5 text-2xl leading-10">{{ bookingInfo().eventName }}</h2>

        <div class="mt-3 flex gap-4 md:gap-3">
          <div class="flex items-center gap-2 text-sm text-gray-600">
            <img src="/Calendar.svg" alt="Calendar icon" class="h-5 w-5" />
            <span class="leading-5">{{ bookingInfo().eventDate }}</span>
          </div>
          <div class="flex items-center gap-2 text-sm text-gray-600">
            <img src="/Clock.svg" alt="Clock icon" class="h-5 w-5" />
            <span class="leading-5">{{ bookingInfo().eventTime }}</span>
          </div>
        </div>

        <div class="mt-5 flex items-center gap-2 md:mt-3">
          <img src="/Location.svg" alt="Location" class="h-5 w-5" />
          <span class="text-sm font-normal leading-5 text-neutral-900">
            {{ bookingInfo().location }}
          </span>
        </div>

        <div class="mt-5 flex items-center gap-2 md:mt-3">
          <img src="/Headphone.svg" alt="Headphone" class="h-5 w-5" />
          <span class="text-sm font-normal leading-5 text-neutral-900">
            {{ bookingInfo().contactNumber }}
          </span>
        </div>
      </div>

      <div class="mx-3 mt-5 flex items-center justify-center rounded-xl bg-[#0BAB68] px-6 py-5">
        <div class="flex w-full max-w-full flex-col items-center">
          <h2 class="text-sm leading-5 text-[#FFFFFF]">Booking ID</h2>
          <h2 class="mt-2 break-all text-center text-xl leading-6 text-[#FFFFFF] max-w-full overflow-wrap-anywhere word-wrap">
            {{ bookingInfo().bookingId }}
          </h2>
        </div>
      </div>

      <h2 class="mx-2 mt-5 max-w-72 text-xs leading-4 text-[#747474]">
        Note: Show this ticket at the venue with a photo ID for entry and further instructions.
      </h2>

      <div class="mx-3 mt-5 flex flex-col items-start justify-center rounded-[1.875rem] bg-[#F5FBFF] p-5">
        <h2 class="max-w-64 text-xs leading-5 text-[#4A4A4A]">
          Click the link to sign the waiver for the activities you are about to join
        </h2>
        <button class="mt-1 text-sm font-normal leading-5 text-[#5171c7] underline underline-offset-2">
          waiveradv1atsemnox.in
        </button>
      </div>

      <div class="flex w-full items-center justify-center py-4">
        <button
          class="flex items-center gap-2 text-sm text-[#5171C7] underline underline-offset-4 transition-colors hover:text-[#3954b4]"
        >
          <img src="/Download.svg" alt="Download Icon" class="h-4 w-4" />
          Download Receipt
        </button>
      </div>
    </div>
  }
</div>

<app-footer>
  <footer class="flex w-full flex-col items-center justify-center bg-white px-5 py-5 gap-3">
    <button
      (click)="simulateLoading()"
      class="flex items-center justify-center gap-2 rounded-full border border-blue-500 px-4 py-2 text-sm font-medium text-blue-500 hover:bg-blue-50"
    >
      Test Loading State
    </button>

    <button
      class="flex w-[22rem] items-center justify-center gap-2 rounded-full border border-black px-6 py-3 text-base font-medium text-black"
    >
      <img src="/Home.svg" alt="Home Icon" class="h-5 w-5" />
      Home
    </button>
  </footer>
</app-footer>
