<div class="flex flex-col items-center pb-32">
    <img
      src="/Success.svg"
      alt="Calendar icon"
      class="w-12 h-12 mt-12"
    />
    <h2 class="text-2xl leading-10 mt-3 text-[#0BAB68]">
      Congratulations!
    </h2>
    <h2 class="text-lg leading-6 font-semibold text-[#171717]">
      Your booking is confirmed
    </h2>
    <h2 class="text-sm leading-5 mt-3 text-center text-[#747474]">
      You will receive a confirmation mail with your booking ID.
    </h2>
  
    <div class="w-full max-w-[21.5625rem] mt-6 p-2 rounded-[1.875rem] bg-white">
      <img
        src="/card.jpg"
        alt="Reservation"
        class="w-full h-[10.75rem] rounded-[1.5rem] object-cover md:rounded-[1.875rem]"
      />
      <div class="ml-4">
        <h2 class="text-2xl leading-10 mt-5">
          {{ eventName() }}
        </h2>
  
        <div class="flex gap-4 mt-3 md:gap-3">
          <div class="flex items-center gap-2 text-sm text-gray-600">
            <img
              src="/Calendar.svg"
              alt="Calendar icon"
              class="w-5 h-5"
            />
            <span class="leading-5">{{ eventDate() }}</span>
          </div>
          <div class="flex items-center gap-2 text-sm text-gray-600">
            <img
              src="/Clock.svg"
              alt="Clock icon"
              class="w-5 h-5"
            />
            <span class="leading-5">{{ eventTime() }}</span>
          </div>
        </div>
  
        <div class="flex items-center gap-2 mt-5 md:mt-3">
          <img src="/Location.svg" alt="Location" class="w-5 h-5" />
          <span class="text-sm leading-5 font-normal text-neutral-900">
            {{ location() }}
          </span>
        </div>
  
        <div class="flex items-center gap-2 mt-5 md:mt-3">
          <img src="/Headphone.svg" alt="Headphone" class="w-5 h-5" />
          <span class="text-sm leading-5 font-normal text-neutral-900">
            {{ contactNumber() }}
          </span>
        </div>
      </div>
  
      <div
        class="flex items-center justify-center mt-5 mx-3 px-6 py-5 rounded-xl bg-[#0BAB68]"
      >
        <div class="flex flex-col items-center break-words">
          <h2 class="text-sm leading-5 text-[#FFFFFF]">
            Booking ID
          </h2>
          <h2 class="text-xl leading-6 mt-2 text-[#FFFFFF]">
            {{ bookingId() }}
          </h2>
        </div>
      </div>
  
      <h2
        class="max-w-72 mx-2 mt-5 text-xs leading-4 text-[#747474]"
      >
        Note: Show this ticket at the venue with a photo ID for entry and further
        instructions.
      </h2>
  
      <div
        class="flex flex-col items-start justify-center mx-3 mt-5 p-5 rounded-[1.875rem] bg-[#F5FBFF]"
      >
        <h2
          class="max-w-64 text-xs leading-5 text-[#4A4A4A]"
        >
          Click the link to sign the waiver for the activities you are about to
          join
        </h2>
        <button
          class="text-sm leading-5 mt-1 font-normal text-[#5171c7] underline underline-offset-2"
        >
          waiveradv1atsemnox.in
        </button>
      </div>
  
      <div class="flex items-center justify-center w-full py-4">
        <button
          class="flex items-center gap-2 text-sm underline underline-offset-4 text-[#5171C7] transition-colors hover:text-[#3954b4]"
        >
          <img src="/Download.svg" alt="Download Icon" class="w-4 h-4" />
          Download Receipt
        </button>
      </div>
    </div>
  </div>
  <app-footer>
    <footer class="flex justify-center items-center w-full py-5 bg-white px-5">
      <button
      class="flex justify-center items-center gap-2 w-[22rem] px-6 py-3 border border-black rounded-full text-base font-medium text-black"
    >
      <img src="/Home.svg" alt="Home Icon" class="w-5 h-5" />
      Home
    </button>
    </footer>
    
  </app-footer>