import { Component } from '@angular/core';
import { Footer } from '../footer/footer';
import { signal } from '@angular/core';

@Component({
  selector: 'app-booking-confirmation',
  imports: [Footer],
  templateUrl: './booking-confirmation.html',
  styleUrl: './booking-confirmation.css'
})
export class BookingConfirmation {
  // Dummy API response
  private readonly apiResponse = {
    eventName: 'Ultimate Adventure Party',
    eventDate: '21 Nov, Tue',
    eventTime: '09:00 AM - 03:00 PM',
    location: 'Semnox Luna',
    contactNumber: '+13013399800',
    bookingId: 'SEMNOXID1234',
  };

  // Signals for each field
  readonly eventName = signal(this.apiResponse.eventName);
  readonly eventDate = signal(this.apiResponse.eventDate);
  readonly eventTime = signal(this.apiResponse.eventTime);
  readonly location = signal(this.apiResponse.location);
  readonly contactNumber = signal(this.apiResponse.contactNumber);
  readonly bookingId = signal(this.apiResponse.bookingId);
}
