import { Component } from '@angular/core';
import { Footer } from '../footer/footer';
import { signal } from '@angular/core';

@Component({
  selector: 'app-booking-confirmation',
  imports: [Footer],
  templateUrl: './booking-confirmation.html',
  styleUrl: './booking-confirmation.css'
})
export class BookingConfirmation {
  // Dummy API response
  readonly bookingInfo = signal({
    eventName: 'Ultimate Adventure Party',
    eventDate: '21 Nov, Tue',
    eventTime: '09:00 AM - 03:00 PM',
    location: 'Semnox Luna',
    contactNumber: '+13013399800',
    bookingId: 'SEMNOXID1234',
  });
}
