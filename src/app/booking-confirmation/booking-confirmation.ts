import { Component } from '@angular/core';
import { Footer } from '../footer/footer';
import { signal } from '@angular/core';
import { BookingConfirmationSkeleton } from '../booking-confirmation-skeleton/booking-confirmation-skeleton';

@Component({
  selector: 'app-booking-confirmation',
  imports: [Footer,BookingConfirmationSkeleton],
  templateUrl: './booking-confirmation.html',
  styleUrl: './booking-confirmation.css'
})
export class BookingConfirmation {
  // Loading state
  readonly isLoading = signal(false);

  // Dummy API response
  readonly bookingInfo = signal({
    eventName: 'Ultimate Adventure Party',
    eventDate: '21 Nov, Tue',
    eventTime: '09:00 AM - 03:00 PM',
    location: 'Semnox Luna',
    contactNumber: '+13013399800',
    bookingId: 'SEMNOXID1234',
  });

  // Method to simulate loading (for testing purposes)
  simulateLoading() {
    this.isLoading.set(true);
    setTimeout(() => {
      this.isLoading.set(false);
    }, 3000);
  }
}
