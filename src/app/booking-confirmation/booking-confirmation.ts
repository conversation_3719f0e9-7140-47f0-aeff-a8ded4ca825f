import { Component, OnInit } from '@angular/core';
import { Footer } from '../footer/footer';
import { signal } from '@angular/core';
import { BookingConfirmationSkeleton } from '../booking-confirmation-skeleton/booking-confirmation-skeleton';

@Component({
  selector: 'app-booking-confirmation',
  imports: [Footer,BookingConfirmationSkeleton],
  templateUrl: './booking-confirmation.html',
  styleUrl: './booking-confirmation.css'
})
export class BookingConfirmation implements OnInit {
  // Loading state - starts as true to show skeleton initially
  readonly isLoading = signal(true);

  // Dummy API response
  readonly bookingInfo = signal({
    eventName: 'Ultimate Adventure Party',
    eventDate: '21 Nov, Tue',
    eventTime: '09:00 AM - 03:00 PM',
    location: 'Semnox Luna',
    contactNumber: '+13013399800',
    bookingId: 'SEMNOXID1234',
  });

  ngOnInit() {
    // Simulate loading data for 3 seconds
    setTimeout(() => {
      this.isLoading.set(false);
    }, 3000);
  }
}
