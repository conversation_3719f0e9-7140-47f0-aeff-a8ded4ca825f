import { Injectable } from "@angular/core";
import { NewTaskData } from "./tasks";

@Injectable({providedIn: 'root'})
export class TasksServices{
    tasks =[
  {
    id:'t1',
    userId:'u1',
    title:'Learn Angular',
    summary:'This is the summary of task 1',
    dueDate:'2025-01-01'
},
{
  id:'t2',
    userId:'u2',
    title:'Learn React',
    summary:'This is the summary of task 2',
    dueDate:'2025-01-02'
},
{
  id:'t3',
    userId:'u3',
    title:'Learn Vue',
    summary:'This is the summary of task 3',
    dueDate:'2025-01-03'
}
]
    getTasksForUser(userId: string) {
        return this.tasks.filter((task) => task.userId === userId);
    }
    
    completeTask(id: string) {
        this.tasks = this.tasks.filter((task) => task.id !== id);
    }
    
    addTask(taskData: NewTaskData, userId: string) {
        this.tasks.unshift({
        id: new Date().getTime().toString(),
        userId: userId,
        title: taskData.title,
        summary: taskData.summary,
        dueDate: taskData.dueDate
        });
    }

    removeTask(id:string){
         this.tasks = this.tasks.filter((task)=>task.id !==id)
    }
}