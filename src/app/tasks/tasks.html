
@if(isAddTaskOpen)
{
    <app-new-task (cancel)="onCloseNewTask()" (add)="onSubmitTask($event)"></app-new-task>
}
<section id="tasks">
<header>
    <h2>{{user()?.name}}'s tasks</h2>
    <menu>
        <button (click)="onAddTask()">
            Add Task
        </button>
    </menu>
</header>
<ul>
@for(task of selectedUserTasks; track task.id){
    <li>
        <app-task [task]="task" (complete)="onCompleteTasks($event)"></app-task>
    </li>
}
</ul>
</section>

