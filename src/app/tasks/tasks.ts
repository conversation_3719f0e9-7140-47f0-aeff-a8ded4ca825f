import { Component, input } from '@angular/core';
import { User } from '../../user/user';
import { Task } from './task/task';
import { NewTask } from '../new-task/new-task';
import { TasksServices } from './tasks.services';

export type UserTask ={
  id:string;
  userId:string;
  title:string;
  summary:string;
  dueDate:string;
}

@Component({
  selector: 'app-tasks',
  imports: [Task,NewTask],
  templateUrl: './tasks.html',
  styleUrl: './tasks.css'
})
export class Tasks {
user = input<User>()
isAddTaskOpen = false;

constructor(private tasksService:TasksServices){

}
  get selectedUserTasks() {
    return this.tasksService.getTasksForUser(this.user()!.id);
  }
  onAddTask(){
    this.isAddTaskOpen = true;
  }
  onCloseNewTask(){
    this.isAddTaskOpen = false;
  }
  onCompleteTasks(id: string) {
    this.tasksService.completeTask(id);
  }
  onSubmitTask(taskData: NewTaskData) {
    this.tasksService.addTask(taskData, this.user()!.id);
    this.isAddTaskOpen = false;
  }
}
 export type NewTaskData = {
  title: string;
  summary: string;
  dueDate: string;
};