import { Component,Input,Output,EventEmitter } from '@angular/core';
import { UserTask } from '../tasks';
import {DatePipe} from '@angular/common';

@Component({
  selector: 'app-task',
  imports: [DatePipe],
  templateUrl: './task.html',
  styleUrl: './task.css'
})
export class Task {
  @Input() task!:UserTask;
  @Output() complete =new EventEmitter();

onCompleteTasks(){
  this.complete.emit(this.task.id);
}

}
