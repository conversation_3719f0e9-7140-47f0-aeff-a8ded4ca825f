#tasks {
  padding: 1rem;
  border-radius: 8px;
  max-height: 60vh;
  overflow: auto;
  background-color: #3a2c54;
}

header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 2rem;
  gap: 1rem;
}

h2 {
  margin: 0;
  font-size: 0.9rem;
  width: 60%;
  text-wrap: balance;
}

menu {
  margin: 0;
  padding: 0;
}

menu button {
  font: inherit;
  cursor: pointer;
  background-color: #9965dd;
  border-radius: 4px;
  border: none;
  padding: 0.35rem 0.8rem;
  font-size: 0.9rem;
}

menu button:hover,
menu button:active {
  background-color: #a565dd
}

ul {
  list-style: none;
  margin: 1rem 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-height: 50vh;
  overflow: auto;
}

@media (min-width: 768px) {
  h2 {
    font-size: 1.25rem;
  }

  menu {
    width: auto;
  }
}