import { Component } from '@angular/core';
import { ReservationCardComponent } from '../reservation-card/reservation-card';
import { SummaryCard } from '../summary-card/summary-card';
import { GuestForm } from '../guest-form/guest-form';
import { PriceDetails } from '../price-details/price-details';
import { RouterOutlet } from '@angular/router';
import { HeaderComponent } from '../../header/header';
import { DiscountCard } from '../discount-card/discount-card';
import { UserComponent } from '../../user/user';
import { PaymentOptions } from '../payment-options/payment-options';
import { Footer } from '../footer/footer';

@Component({
  selector: 'app-billing-page',
  imports: [ RouterOutlet,
    HeaderComponent,
    UserComponent,
    ReservationCardComponent,
    DiscountCard,
    PaymentOptions,
    PriceDetails,
    GuestForm,
    SummaryCard,
  Footer ],
  templateUrl: './billing-page.html',
  styleUrl: './billing-page.css'
})
export class BillingPage {

}
