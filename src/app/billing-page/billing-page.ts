import { Component } from '@angular/core';
import { ReservationCardComponent } from '../reservation-card/reservation-card';
import { SummaryCard } from '../summary-card/summary-card';
import { GuestForm } from '../guest-form/guest-form';
import { PriceDetails } from '../price-details/price-details';
import { DiscountCard } from '../discount-card/discount-card';
import { PaymentOptions } from '../payment-options/payment-options';
import { Footer } from '../footer/footer';

@Component({
  selector: 'app-billing-page',
  imports: [
    ReservationCardComponent,
    DiscountCard,
    PaymentOptions,
    PriceDetails,
    GuestForm,
    SummaryCard,
  Footer ],
  templateUrl: './billing-page.html',
  styleUrl: './billing-page.css'
})
export class BillingPage {

}
