import { Component,Output ,EventEmitter} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { NewTaskData } from '../tasks/tasks';

@Component({
  selector: 'app-new-task',
  imports: [FormsModule],
  templateUrl: './new-task.html',
  styleUrl: './new-task.css'
})
export class NewTask {
 @Output() cancel = new EventEmitter<void>();
 @Output() add = new EventEmitter<NewTaskData>();
title = '';
summary = '';
dueDate = ''

 onCancel(){
  this.cancel.emit();
 }

onSubmit(){
this.add.emit({
  title: this.title,
  summary: this.summary,
  dueDate: this.dueDate
})
 }
}
