<div class="backdrop" (click)="onCancel()"></div>
<dialog open>
  <h2>Add Task</h2>
  <form (ngSubmit)="onSubmit()" >
    <p>
      <label for="title">Title</label>
      <input type="text" id="title" name="title" [(ngModel)]="title"/>
    </p>

    <p>
      <label for="summary">Summary</label>
      <textarea id="summary" rows="5" name="summary" [(ngModel)]="summary"></textarea>
    </p>

    <p>
      <label for="due-date">Due Date</label>
      <input type="date" id="due-date" name="due-date" [(ngModel)] ="dueDate" />
    </p>

    <p class="actions">
      <button type="button" (click)="onCancel()">Cancel</button>
      <button type="submit">Create</button>
    </p>
  </form>
</dialog>