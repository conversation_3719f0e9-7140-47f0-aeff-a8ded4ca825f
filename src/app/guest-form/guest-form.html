<div
  class="flex flex-col gap-6 w-full max-w-[22rem] md:max-w-[52rem] p-5 md:p-6 rounded-[1.875rem] bg-white"
>
  <div class="flex items-center gap-2">
    <span
      class="text-sm leading-[1.125rem] font-semibold text-neutral-900"
      >Continue as Guest</span
    >
  </div>
  <div class="flex flex-col gap-4 w-full md:flex-row md:gap-5">
    <div class="flex flex-col gap-2 flex-1">
      <label class="text-xs font-normal text-neutral-900">Name</label>
      <input
        type="text"
        placeholder="Enter Name"
        class="w-full h-12 px-4 py-[0.9375rem] border border-[#dce3f4] rounded-xl text-sm font-normal text-[#747474] focus:outline-none focus:ring-2 focus:ring-[#5171c7]"
      />
    </div>
    <div class="flex flex-col gap-2 flex-1">
      <label class="text-xs font-normal text-neutral-900"
        >Phone Number</label
      >
      <input
        type="text"
        placeholder="Enter phone number"
        class="w-full h-12 px-4 py-[0.9375rem] border border-[#dce3f4] rounded-xl text-sm font-normal text-[#747474] focus:outline-none focus:ring-2 focus:ring-[#5171c7]"
      />
    </div>
    <div class="flex flex-col gap-2 flex-1">
      <label class="text-xs font-normal text-neutral-900"
        >Email Address</label
      >
      <input
        type="text"
        placeholder="Enter email address"
        class="w-full h-12 px-4 py-[0.9375rem] border border-[#dce3f4] rounded-xl text-sm font-normal text-[#747474] focus:outline-none focus:ring-2 focus:ring-[#5171c7]"
      />
    </div>
  </div>
  <div
    class="flex items-start justify-center w-full gap-5 md:gap-5 md:items-center md:justify-start"
  >
    <div class="flex flex-col md:flex-row items-center gap-1">
      <span class="text-xs text-[#747474]">New customer?</span>
              <button
          class="text-sm underline underline-offset-2 font-normal text-[#5171c7]"
        >
          Register
        </button>
    </div>
    <div class="flex flex-col md:flex-row items-center gap-1">
      <span class="text-xs text-[#747474]">Existing customer?</span>
              <button
          class="text-sm underline underline-offset-2 font-normal text-[#5171c7]"
        >
          Login
        </button>
    </div>
  </div>
</div>
