/* Skeleton loader animations */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
  }
  
  .skeleton-dark {
    background: linear-gradient(90deg, #e8e8e8 25%, #d0d0d0 50%, #e8e8e8 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
  }
  
  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }
  
  /* Skeleton specific shapes */
  .skeleton-circle {
    border-radius: 50%;
  }
  
  .skeleton-rounded {
    border-radius: 0.375rem;
  }
  
  .skeleton-rounded-lg {
    border-radius: 0.5rem;
  }
  
  .skeleton-rounded-xl {
    border-radius: 0.75rem;
  }
  
  .skeleton-text {
    border-radius: 0.25rem;
    height: 1em;
  }