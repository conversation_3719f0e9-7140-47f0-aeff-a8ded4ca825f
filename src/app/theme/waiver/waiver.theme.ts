/**
 * @fileoverview
 * Site level theme configuration for Waiver application
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @created 2024-09-18
 */

export interface ThemeObject {
    [key: string]: any;
}

export const SiteWaiverTheme: ThemeObject = {
    // btn_primary: {
    //     bg_color: 'bg-gray-500'
    // }
};

export const HeaderTheme: ThemeObject = {
    // template: 'style-1',
};

export const FooterTheme: ThemeObject = {
    // theme: '4col',
};

export const LandingPageTheme: ThemeObject = {
    // btn_primary: {
    //     bg_color: 'bg-gray-500'
    // }
    // register_btn: { 
    //     bgGradient: "bg-gradient-to-b",
    //     bgGradientFrom: "from-primary-500",
    //     bgGradientTo: "to-accent-500", 
    //     hover: {
    //         bgGradientFrom: 'from-primary-500',
    //         bgGradientTo: 'to-accent-600',
    //     },
    // }
};

export const AboutPageTheme: ThemeObject = {
    // btn_primary: {bg_color: 'bg-gray-900'}
};

export const RegisterFormTheme: ThemeObject = {
    // template: 'grid',
};

export const AddMinorFormTheme: ThemeObject = {
    // template: 'list'
};