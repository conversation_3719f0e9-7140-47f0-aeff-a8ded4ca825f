import { ComponentFixture, TestBed } from '@angular/core/testing';

import { DicountAppliedModal } from './dicount-applied-modal';

describe('DicountAppliedModal', () => {
  let component: DicountAppliedModal;
  let fixture: ComponentFixture<DicountAppliedModal>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [DicountAppliedModal]
    })
    .compileComponents();

    fixture = TestBed.createComponent(DicountAppliedModal);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
