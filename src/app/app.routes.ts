import { Routes } from '@angular/router';
import { BillingPage } from './billing-page/billing-page';
import { BookingConfirmation } from './booking-confirmation/booking-confirmation';
import { BookingFail } from './booking-fail/booking-fail';
import { DicountAppliedModal } from './dicount-applied-modal/dicount-applied-modal';

export const routes: Routes = [
  { path: 'billing', component: BillingPage },
  { path: 'success', component: BookingConfirmation },
  { path: 'fail', component: BookingFail },
  { path: '', redirectTo: 'billing', pathMatch: 'full' },
];
