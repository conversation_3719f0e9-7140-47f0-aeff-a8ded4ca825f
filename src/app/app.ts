import { Component } from '@angular/core';
import { RouterOutlet } from '@angular/router';
import { HeaderComponent } from '../header/header';
import { User, UserComponent } from '../user/user';
import { DUMMY_USERS } from '../dummy-users';
import { Tasks } from './tasks/tasks';
import { ReservationCardComponent } from './reservation-card/reservation-card';
import { DiscountCard } from './discount-card/discount-card';
import { PaymentOptions } from './payment-options/payment-options';
import { PriceDetails } from './price-details/price-details';
import { GuestForm } from './guest-form/guest-form';
import { SummaryCard } from './summary-card/summary-card';

@Component({
  selector: 'app-root',
  imports: [
    RouterOutlet,
  ],
  templateUrl: './app.html',
  styleUrl: './app.css',
})
export class App {
  protected title = 'angular-first-app';
  users = DUMMY_USERS;
  selectedUser!: User;

  handleGreet(user: User) {
    this.selectedUser = user;
  }
}
