/* You can add global styles to this file, and also import other style files */
@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: 'Onest', sans-serif;
  background-color: #F1F4FE;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.squircle {
  --squircle-displacement: 30pt;

  clip-path: shape(
    from 0 var(--squircle-displacement),
    curve to var(--squircle-displacement) 0 with 0 0 / 0 0,
    hline to calc(100% - var(--squircle-displacement)),
    curve to 100% var(--squircle-displacement) with 100% 0 / 100% 0,
    vline to calc(100% - var(--squircle-displacement)),
    curve to calc(100% - var(--squircle-displacement)) 100% with 100% 100% /
      100% 100%,
    hline to var(--squircle-displacement),
    curve to 0 calc(100% - var(--squircle-displacement)) with 0 100% / 0 100%,
    close
  );
}

.squircle-lg {
  --squircle-displacement: 50pt;

  clip-path: shape(
    from 0 var(--squircle-displacement),
    curve to var(--squircle-displacement) 0 with 0 0 / 0 0,
    hline to calc(100% - var(--squircle-displacement)),
    curve to 100% var(--squircle-displacement) with 100% 0 / 100% 0,
    vline to calc(100% - var(--squircle-displacement)),
    curve to calc(100% - var(--squircle-displacement)) 100% with 100% 100% /
      100% 100%,
    hline to var(--squircle-displacement),
    curve to 0 calc(100% - var(--squircle-displacement)) with 0 100% / 0 100%,
    close
  );
}