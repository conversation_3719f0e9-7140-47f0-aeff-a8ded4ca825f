import { Component, EventEmitter, input, Input, Output } from '@angular/core';

@Component({
  selector: 'app-user',
  imports: [],
  templateUrl: './user.html',
  styleUrl: './user.css',
})
export class UserComponent {
  //@Input() user!:User
  user = input<User>();
  selected = input<boolean>();
  @Output() select = new EventEmitter();

  onSelectUser() {
    this.select.emit(this.user());
  }
}

export type User = {
  id: string;
  name: string;
  avatar: string;
};
