header {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  width: 90%;
  max-width: 50rem;
  margin: 0 auto 2rem auto;
  text-align: center;
  background: linear-gradient(
    to bottom,
    #2c0a4c,
    #450d80
  );
  padding: 1rem;
  border-bottom-right-radius: 12px;
  border-bottom-left-radius: 12px;
  box-shadow: 0 1px 8px rgba(0, 0, 0, 0.6);
}

img {
  width: 3.5rem;
  object-fit: contain;
}

h1 {
  font-size: 1.25rem;
  margin: 0;
  padding: 0;
}

p {
  margin: 0;
  font-size: 0.8rem;
  text-wrap: balance;
}

@media (min-width: 768px) {
  header {
    padding: 2rem;
  }

  img {
    width: 4.5rem;
  }

  h1 {
    font-size: 1.5rem;
    margin: 0;
    padding: 0;
  }
}